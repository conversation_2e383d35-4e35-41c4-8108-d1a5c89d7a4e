<DCUpKafka>
    <common>
        <log>
            <!-- <param name="logpath">/codebill/AH_MYSQL/src_5g/CTPC/DCUpKafka/log</param> -->
            <param name="logpath">/public/codebill/AH_MYSQL/src_5g/CTPC/DCUpKafka/log</param>
            <param name="loglevel">DEBUG</param><!--DEBUG, INFO, WARNING, ERROR-->
        </log>    

        <dbconnect>
            <param name="dbtype">mysql</param><!--mysql, pg，二选一-->
            <param name="host">**************</param>
            <param name="port">3308</param>
            <param name="user">root</param>
            <param name="pass">mysql</param><!--明文密文都支持，加解密用desTool工具-->
            <param name="db">bp-ticket</param>
        </dbconnect>

        <kafka>
            <!-- <param name="bootstrap_servers">**************:9092</param> -->
            <param name="safe_cert">0</param><!--0-不认证，1-认证-->
            <param name="bootstrap_servers">**************:9092</param>
            <param name="security_protocol">SASL_PLAINTEXT</param><!--自定义-->
            <param name="sasl_mechanism">PLAIN</param><!--自定义-->
            <param name="sasl_plain_username">itete</param><!--自定义-->
            <param name="sasl_plain_password">xH9%iO2*tK23aR9f</param><!--自定义-->
            <param name="topic">test1</param>
        </kafka>

        <taskThread>
            <param name="thread_num">5</param>
            <param name="start_end_time_range">10|60</param><!--单位：秒，不配置，则默认取task里面第一条end_time，如果配置，则取send_time前范围时间内的随机时间，例如send_time为2025-05-23 14:30:30，则end_time会随机取2025-05-23 14:29:30 ~ 2025-05-23 14:30:20之间的时间, start_time再取end_time前范围时间内的随机时间-->
        </taskThread>

        <zk><!--多进程容灾，不同主机启动多个进程，则只会有一个进程在处理，如果该进程掉了，则由其他进程接管-->
            <param name="zkSwitch">1</param><!--0-不使用，1-使用-->
            <param name="zkAddr">**************:50810,**************:50810,**************:50810</param><!--zk地址-->
            <param name="zkNode">/dcupKafka/temporary/node</param><!--zk临时节点，注册信息为：IP|进程号，多个进程同时启动，注册成功的才会提取监控信息，可用于一主多备部署（必须zkAddr和zkNode一致的才属于主备关系）-->
        </zk>
    </common>

    <businessCfg>
        <taskSQL>
            <param name="sql_query">SELECT msg_id,system,subsys,Module,Module_name,Latn_id,Latn_name,id_code,id_name,batch_id,startTime,endTime,sendTime,areaCode,areaName,Kpi_Code,Kpi_name,Kpi_value,Create_date,Deal_flag,deal_date FROM kafka_upload_messages where Deal_flag=0 LIMIT 10</param>
            <param name="sql_update">update kafka_upload_messages set Deal_flag=1,deal_date=now() where msg_id in (%s)</param>
            <param name="sql_error">update kafka_upload_messages set Deal_flag=4,deal_date=now() where msg_id in (%s)</param>
            <param name="sql_timeout">update kafka_upload_messages set Deal_flag=5,deal_date=now() where msg_id in (%s)</param>
            <param name="sql_delete">delete from kafka_upload_messages where msg_id in (%s)</param>
            <param name="sql_insert">insert into kafka_upload_messages_[MM](msg_id,system,subsys,Module,Module_name,Latn_id,Latn_name,id_code,id_name,batch_id,startTime,endTime,sendTime,areaCode,areaName,Kpi_Code,Kpi_name,Kpi_value,Create_date,Deal_flag,deal_date) values('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')</param>
        </taskSQL>
        
        <otherCfg>
            <param name="latn_kpicode_replace">CY1002|CZ1001L04;CY1003|CZ1003L06;CL1001|CL1001L09</param><!--格式：idcode1|kpicode1;idcode2|kpicode2;idcode3|kpicode3-->
            <param name="check_kpicode_num">CY1001|1;CY1002|3;CL1001|5</param><!--格式：idcode1|子场景个数;idcode2|子场景个数   不配置或者子场景个数配置为0，则不进行合并校验，否则batch_id，latn_id，id_code维度取的条数必须与配置条数一致才处理-->
            <param name="merge_max_secs">60</param><!--单位：秒，不配置，默认60秒，需要合并发送的id_code最大等待时间，超过这个时间，则更新为状态5-->
        </otherCfg>

    </businessCfg>

</DCUpKafka>
