import xml.dom.minidom
import os,io,traceback,threading

class DCConfig:
    def __init__(self, config_file, config_node):
        self.m_lock = threading.Lock()  # 创建一个线程锁，用于保护配置文件读写操作。
        self.m_config_file = config_file
        self.m_config_node = config_node
        self.m_dict_config = {}
        # 记录配置文件时间
        self.m_file_time = os.path.getmtime(self.m_config_file)  # 获取文件的最后修改时间
        self.load_config()  # 加载配置文件内容到内存中。


    def load_config(self):
        try:
            print(self.m_config_file, self.m_config_node)
            self.m_dict_config = {}
            dom = xml.dom.minidom.parse(self.m_config_file)
            root = dom.documentElement
            for config_node in self.m_config_node:
                nodes = root.getElementsByTagName(config_node)
                for node in nodes:
                    params = node.getElementsByTagName("param")
                    dicValue = {}
                    for param in params:
                        if param.firstChild == None:
                            dicValue[param.getAttribute("name")] = None
                        else:
                            dicValue[param.getAttribute("name")] = param.firstChild.data
                    if config_node in self.m_dict_config.keys():
                        self.m_dict_config[config_node].append(dicValue)
                    else:
                        self.m_dict_config[config_node] = [dicValue]    
        except Exception as err:
            # 将输出重定向到字符串缓冲区
            buf = io.StringIO()
            traceback.print_exc(file=buf)
            # 获取缓冲区的内容
            error_msg = buf.getvalue()
            print("err[%s][%s]" % (str(err), str(error_msg)))    


    def load_config_new(self):
        try:
            self.m_dict_config = {}
            dom = xml.dom.minidom.parse(self.m_config_file)
            root = dom.documentElement
            for config_node in self.m_config_node:
                lstNodeName = config_node.split("/")
                current_node = root
                for n in range(0, len(lstNodeName)):
                    nodes = current_node.getElementsByTagName(lstNodeName[n])
                    if nodes:
                        current_node = nodes  # 修改这里，保存所有匹配的节点
                        if n != len(lstNodeName) - 1:  # 如果不是最后一个节点，则继续遍历
                            current_node = current_node[0]  # 取第一个匹配的节点
                    else:
                        current_node = None
                        break

                if current_node is not None:
                    dicValueList = []
                    for node in current_node:  # 遍历所有匹配的节点
                        params = node.getElementsByTagName("param")
                        dicValue = {}
                        for param in params:
                            if param.firstChild is None:
                                dicValue[param.getAttribute("name")] = None
                            else:
                                dicValue[param.getAttribute("name")] = param.firstChild.data
                        dicValueList.append(dicValue)
                    if config_node in self.m_dict_config.keys():
                        self.m_dict_config[config_node].extend(dicValueList)
                    else:
                        self.m_dict_config[config_node] = dicValueList
        except Exception as err:
            # 将输出重定向到字符串缓冲区
            buf = io.StringIO()
            traceback.print_exc(file=buf)
            # 获取缓冲区的内容
            error_msg = buf.getvalue()
            print("err[%s][%s]" % (str(err), str(error_msg)))


    # update config
    def update_config(self):
        self.m_lock.acquire()
        # 获取配置文件时间
        file_time = os.path.getmtime(self.m_config_file)  # 获取文件的最后修改时间
        if file_time > self.m_file_time:  # 如果配置文件有更新，则重新加载配置文件内容到内存中，并更新时间记录。
            self.m_file_time = file_time  # 更新时间记录，以便下次检查是否需要重新加载配置文件。
            self.load_config()  # 重新加载配置文件内容到内存中。
            self.m_lock.release()  # 释放线程锁。
            return True  # 返回True，表示配置文件有更新。
        self.m_lock.release()  # 释放线程锁。
        return False


    def get_single_value(self, node, param_name):
        try:
            self.update_config()  # 检查配置文件是否有更新，如果有则重新加载配置文件内容到内存中。
            self.m_lock.acquire()
            if node in self.m_dict_config.keys():
                for dicValue in self.m_dict_config[node]:
                    if param_name in dicValue.keys():
                        self.m_lock.release()  # 释放线程锁。
                        return dicValue[param_name]  # 返回参数值。
            self.m_lock.release()  # 释放线程锁。
            return None  # 返回空值。
        except Exception as err:
            # 将输出重定向到字符串缓冲区
            buf = io.StringIO()
            traceback.print_exc(file=buf)
            # 获取缓冲区的内容
            error_msg = buf.getvalue()
            print("err[%s][%s]" % (str(err), str(error_msg)))
            return None  # 返回空值。
        
    
    def get_group_value(self, node):
        try:
            self.update_config()  # 检查配置文件是否有更新，如果有则重新加载配置文件内容到内存中。
            self.m_lock.acquire()
            if node in self.m_dict_config.keys():
                self.m_lock.release()  # 释放线程锁。
                return self.m_dict_config[node]  # 返回参数值。
            self.m_lock.release()  # 释放线程锁。
            return None  # 返回空值。
        except Exception as err:
            # 将输出重定向到字符串缓冲区
            buf = io.StringIO()
            traceback.print_exc(file=buf)
            # 获取缓冲区的内容
            error_msg = buf.getvalue()
            print("err[%s][%s]" % (str(err), str(error_msg)))
            return None  # 返回空值。