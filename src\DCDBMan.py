import pymysql
import psycopg2
import logging2
import io
import traceback
import threading
import datetime
from desTool import ASEUtil

class DCDBMan:
    def __init__(self, db_type, dicDbObj):
        self.m_lock = threading.RLock()
        self.m_dicDbObj = dicDbObj
        self.db_type = db_type.lower()
        self.connection = None
        self.cursor = None
        self.connect()

    def _get_connection(self, passwordStr):
        if self.db_type == 'mysql':
            return pymysql.connect(
                host=self.m_dicDbObj["host"],
                port=int(self.m_dicDbObj["port"]),
                user=self.m_dicDbObj["user"],
                passwd=passwordStr,
                db=self.m_dicDbObj["db"],
                charset='utf8'
            )
        elif self.db_type == 'pg':
            return psycopg2.connect(
                host=self.m_dicDbObj["host"],
                port=self.m_dicDbObj["port"],
                user=self.m_dicDbObj["user"],
                password=passwordStr,
                database=self.m_dicDbObj["db"]
            )
        else:
            raise ValueError("Unsupported database type")

    def connect(self):
        try:
            passwordStr = ASEUtil.decrypted(self.m_dicDbObj["pass"]) or self.m_dicDbObj["pass"]
            self.connection = self._get_connection(passwordStr)
            self.cursor = self.connection.cursor()
            logging2.info(f"Connected to {self.db_type} database successfully")
            return self.cursor
        except Exception as err:
            self._log_error(err, "Failed to connect to database")
            return None
        finally:
            pass

    def _log_error(self, err, message):
        buf = io.StringIO()
        traceback.print_exc(file=buf)
        error_msg = buf.getvalue()
        logging2.error(f"{message}: {str(err)}\n{error_msg}")

    def close(self):
        try:
            self.m_lock.acquire()
            if self.cursor:
                self.cursor.close()
                self.connection.close()
                logging2.info("Database connection closed")
                return True
            return False
        except Exception as err:
            self._log_error(err, "Failed to close database connection")
            return False
        finally:
            self.m_lock.release()

    def rollback(self):
        try:
            if self.connection:
                self.connection.rollback()
                logging2.info("Transaction rolled back")
                return True
            return False
        except Exception as err:
            self._log_error(err, "Failed to rollback transaction")

    def execute_query(self, sqltext, params=None, retry=1):
        try:
            self.m_lock.acquire()
            sqltext = self._replace_month_placeholder(sqltext)
            if self.cursor:
                self.cursor.execute(sqltext, params or ())
                logging2.debug(f"Query executed successfully: {sqltext}")
                return self.cursor.fetchall()
            logging2.debug("No active database connection")
            # self.reconnect()
            return []
        except Exception as err:
            self._log_error(err, f"Failed to execute query: {sqltext}")
            self.reconnect()
            if retry:
                return self.execute_query(sqltext, params, retry - 1)
            return []
        finally:
            self.m_lock.release()

    def execute_sql(self, sqltext, params=None, retry=1):
        try:
            self.m_lock.acquire()
            sqltext = self._replace_month_placeholder(sqltext)
            if self.cursor:
                self.cursor.execute(sqltext, params or ())
                row_effected = self.cursor.rowcount
                self.connection.commit()
                logging2.debug(f"Data executed successfully: {sqltext}")
                return row_effected
            logging2.debug("No active database connection")
            # self.reconnect()
            return 0
        except Exception as err:
            self.rollback()
            self._log_error(err, f"Failed to execute SQL: {sqltext}")
            self.reconnect()
            if retry:
                logging2.warning(f"Retrying SQL execution. Remaining attempts: {retry - 1}")
                return self.execute_sql(sqltext, params, retry - 1)
            return 0
        finally:
            self.m_lock.release()

    def _replace_month_placeholder(self, sqltext):
        if '[MM]' in sqltext:
            formatted_month = str(datetime.datetime.now().month).zfill(2)
            sqltext = sqltext.replace('[MM]', formatted_month)
        return sqltext

    def reconnect(self):
        try:
            if self.cursor:
                self.cursor.close()
                self.connection.close()
            self.cursor = self.connect()
            logging2.info("Reconnected to database")
            return True
        except Exception as err:
            self._log_error(err, "Failed to reconnect to database")
            return False
        finally:
            pass

    def disconnect(self):
        try:
            self.m_lock.acquire()
            if self.cursor:
                self.cursor.close()
                self.connection.close()
                logging2.debug("Disconnected from database")
                return True
            logging2.debug("No active database connection")
            return False
        except Exception as err:
            self._log_error(err, "Failed to disconnect from database")
            return False
        finally:
            self.m_lock.release()