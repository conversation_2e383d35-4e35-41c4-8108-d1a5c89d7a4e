# 多线程执行任务，需要考虑线程安全，使用线程锁，或者使用线程池。
import random
import threading
import time,sys
import logging2
import json
import traceback
import queue
import time
from datetime import datetime  # 正确导入 datetime 类
from kafka import KafkaProducer
from DCConfig import *
from DCDBMan import *


class DCTaskDeal(threading.Thread):
    def __init__(self, configObj, dbmanObj):
        threading.Thread.__init__(self) # 初始化线程，继承父类threading.Thread的__init__方法。
        self.m_configObj = configObj # 配置对象，用于获取配置信息。
        self.m_dbmanObj = dbmanObj # 数据库管理对象，用于执行数据库操作。
        self.initParam()

    
    def initParam(self):
        print(self.m_configObj)
        self.m_kafkaCfgObj = self.m_configObj.get_group_value('common/kafka')[0] # kafka配置对象，用于获取kafka配置信息。
        self.m_taskThreadCfgObj = self.m_configObj.get_group_value('common/taskThread')[0] # 任务线程配置对象，用于获取任务线程配置信息。
        self.m_taskSqlCfgObj = self.m_configObj.get_group_value('businessCfg/taskSQL')[0] # 任务SQL配置对象，用于获取任务SQL配置信息。
        self.m_otherCfgObj = self.m_configObj.get_group_value('businessCfg/otherCfg')[0] # 其他配置对象，用于获取其他配置信息。
        self.m_maxThreadTaskNum = int(self.m_taskThreadCfgObj['thread_num']) # 线程数量，用于创建线程池。
        self.m_endTimeRange = ""
        if self.m_taskThreadCfgObj == None or self.m_taskThreadCfgObj.get('start_end_time_range') == None:
            self.m_endTimeRange = ""
        else:
            self.m_endTimeRange = self.m_taskThreadCfgObj['start_end_time_range'] # 10|60,单位：秒，不配置，则默认取task里面第一条end_time，如果配置，则取send_time前范围时间内的随机时间，例如send_time为2025-05-23 14:30:30，则会随机取2025-05-23 14:29:30 ~ 2025-05-23 14:30:20之间的时间
        self.m_topic = self.m_kafkaCfgObj['topic'] # kafka主题，用于发送消息。
        self.m_isExit = False
        self.m_kafkaData = {}
        self.m_dicDataAll = {}
        self.m_AQueue = queue.Queue(100000) # 创建一个队列，用于存储任务。
        self.createThreadPool()
        self.createKafkaObj()     
        self.initOtherCfg()   

    
    def run(self) -> None:
        oldTime = time.time()
        while True:
            try:
                curTime = time.time()
                if curTime - oldTime >= self.m_merge_max_secs:
                    self.clearTimeOutData()
                    oldTime = curTime
                if self.getExitFlag():
                    logging2.info(f'task deal thread exiting...')
                    if self.m_AQueue.empty() and len(self.m_dicDataAll) == 0:
                        self.releaseKafkaObj()
                        break
                task = self.m_AQueue.get(timeout=5) # 从队列中获取任务。
                logging2.info(f'get task, {task}')
                self.dealTask(task) # 执行任务。
            except:
                continue


    def get_range_endtime(self, task):
        try:
            sendTime = str(task[12])
            endTimeRange = self.m_endTimeRange.split('|')
            if len(endTimeRange) != 2:
                logging2.error(f'start_end_time_range error, start_end_time_range:{self.m_endTimeRange}')
                return False,'',''
            if int(endTimeRange[0]) > int(endTimeRange[1]):
                logging2.error(f'start_start_end_time_range error, start_end_time_range:{self.m_endTimeRange}')
                return False,'',''
            sendTime = datetime.datetime.strptime(sendTime, "%Y-%m-%d %H:%M:%S")
            endTime = sendTime - datetime.timedelta(seconds=random.randint(int(endTimeRange[0]), int(endTimeRange[1])))
            startTime = endTime - datetime.timedelta(seconds=random.randint(int(endTimeRange[0]), int(endTimeRange[1])))
            return True,endTime.strftime("%Y-%m-%d %H:%M:%S"),startTime.strftime("%Y-%m-%d %H:%M:%S")
        except:
            logging2.error(f'get_range_endtime error, start_end_time_range:{self.m_endTimeRange} task:{task} traceback:{traceback.format_exc()}')
            return False,'',''


    def dealTask(self, task):
        try:
            task = list(task)
            task[12] = str(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
            task[20] = str(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
            keyStr = str(task[9])+'|'+str(task[5])+'|'+str(task[7])
            dicTmpLatn = {}
            if str(task[5]) != '999':
                if self.m_dicLatnKpiCode.get(str(task[7])) == None:
                    logging2.error(f'latn_kpicode_replace error, kpi_code:{task[7]}')
                    return
                else:
                    dicTmpLatn['indexLabelCode'] = self.m_dicLatnKpiCode[str(task[7])]
                    dicTmpLatn['indexLabelName'] = '本地网名称'
                    dicTmpLatn['indexLabelValue'] = str(task[6])
            if self.m_dicDataAll.get(keyStr) == None:
                dicJson = {}
                pos = str(task[10]).find('+')
                if pos != -1:
                    dicJson['startTime'] = str(task[10])[:pos]
                else:
                    dicJson['startTime'] = str(task[10])
                pos = str(task[11]).find('+')
                if pos != -1:
                    dicJson['endTime'] = str(task[11])[:pos]
                else:
                    dicJson['endTime'] = str(task[11])
                if self.m_endTimeRange != "":
                    ret,endTime,startTime = self.get_range_endtime(task)
                    if ret:
                        dicJson['endTime'] = endTime
                        dicJson['startTime'] = startTime
                dicJson['sendTime'] = str(task[12])
                dicJson['areaCode'] = str(task[13])
                dicJson['areaName'] = str(task[14])
                dicJson['indexCode'] = str(task[7])
                dicJson['indexName'] = str(task[8])
                dicTmp = {}
                dicTmp['indexLabelCode'] = str(task[15])
                dicTmp['indexLabelName'] = str(task[16])
                dicTmp['indexLabelValue'] = str(task[17])
                dicJson['indexLabels'] = [dicTmp]
                self.m_dicDataAll[keyStr] = {}
                self.m_dicDataAll[keyStr]['time'] = time.time()
                self.m_dicDataAll[keyStr]['task'] = [tuple(task)]
                self.m_dicDataAll[keyStr]['json'] = dicJson
                self.m_dicDataAll[keyStr]['jsonNum'] = 1  # 统计子场景的个数，dicTmpLatn本地网的不统计
                if len(dicTmpLatn) > 0 and dicTmpLatn not in self.m_dicDataAll[keyStr]['json']['indexLabels']:
                    self.m_dicDataAll[keyStr]['json']['indexLabels'].append(dicTmpLatn)
            else:
                dicTmp = {}
                dicTmp['indexLabelCode'] = str(task[15])
                dicTmp['indexLabelName'] = str(task[16])
                dicTmp['indexLabelValue'] = str(task[17])
                self.m_dicDataAll[keyStr]['time'] = time.time()
                if self.m_endTimeRange != "":
                    ret,endTime,startTime = self.get_range_endtime(task)
                    if ret:
                        dicJson['endTime'] = endTime
                        dicJson['startTime'] = startTime
                self.m_dicDataAll[keyStr]['task'].append(tuple(task))
                self.m_dicDataAll[keyStr]['json']['sendTime'] = str(task[12])
                self.m_dicDataAll[keyStr]['json']['indexLabels'].append(dicTmp)
                self.m_dicDataAll[keyStr]['jsonNum'] += 1  # 统计子场景的个数，dicTmpLatn本地网的不统计

            # 检查是否达到子场景条数，如果达到，则可以发送
            if self.checkKpiNum(keyStr, str(task[7])):
                if self.produceMsg(self.m_dicDataAll[keyStr]['json']):
                    if self.taskInsert(self.m_dicDataAll[keyStr]['task']) == 0:
                        self.printErrorMessage('insertFailedAndProduceSucces', keyStr)
                        self.taskError(self.m_dicDataAll[keyStr]['task'])
                    else:
                        if self.taskDelete(self.m_dicDataAll[keyStr]['task']) == 0:
                            self.printErrorMessage('deleteFailedAndProduceSucces', keyStr)
                else:
                    self.printErrorMessage('produceFailed', keyStr)
                    self.taskError(self.m_dicDataAll[keyStr]['task'])
                # 删除字典中的数据
                del self.m_dicDataAll[keyStr]
        except Exception as e:
            logging2.error(f'error:{e} {traceback.print_exc()}')


    def printErrorMessage(self, type, key):
        logging2.error(f'type:{type} key:{key}')


    # 遍历m_dicDataAll，找到里面时间超过m_merge_max_secs的数据，任务更新为异常，且删除m_dicDataAll内存里面的数据
    def clearTimeOutData(self):
        keys_to_delete = []
        try:
            for keyStr in self.m_dicDataAll.keys():
                curTime = time.time()
                if curTime - self.m_dicDataAll[keyStr]['time'] > self.m_merge_max_secs:
                    logging2.info(f'True: keyStr:{keyStr} curTime:{curTime} keyStrTime:{self.m_dicDataAll[keyStr]["time"]} timeOut:{self.m_merge_max_secs}')
                    self.taskTimeout(self.m_dicDataAll[keyStr]['task'])
                    keys_to_delete.append(keyStr)
            for keyStr in keys_to_delete:
                logging2.info(f'Delete keyStr:{keyStr}')
                del self.m_dicDataAll[keyStr]
        except Exception as e:
            logging2.error(f'error:{e} {traceback.print_exc()}')

    # 0. msg_id
    # 1. system
    # 2. subsys
    # 3. Module
    # 4. Module_name
    # 5. Latn_id
    # 6. Latn_name
    # 7. id_code
    # 8. id_name
    # 9. batch_id
    # 10. startTime
    # 11. endTime
    # 12. sendTime
    # 13. areaCode
    # 14. areaName
    # 15. Kpi_Code
    # 16. Kpi_name
    # 17. Kpi_value
    # 18. Create_date
    # 19. Deal_flag
    # 20. deal_date

    def checkKpiNum(self, keyStr, id_code):
        try:
            # 没配置的，直接输出到kafka
            if self.m_dicCheckKpiCode.get(id_code) is None:
                return True
            
            if self.m_dicDataAll.get(keyStr) is not None and self.m_dicDataAll[keyStr].get('jsonNum') is not None and self.m_dicCheckKpiCode.get(id_code) is not None:
                if self.m_dicDataAll[keyStr]['jsonNum'] >= int(self.m_dicCheckKpiCode[id_code]):
                    logging2.info(f'True: keyStr:{keyStr} jsonNum:{self.m_dicDataAll[keyStr]["jsonNum"]} id_code:{id_code} kpiNum:{self.m_dicCheckKpiCode[id_code]}')
                    return True
                else:
                    logging2.info(f'False: keyStr:{keyStr} jsonNum:{self.m_dicDataAll[keyStr]["jsonNum"]} id_code:{id_code} kpiNum:{self.m_dicCheckKpiCode[id_code]}')
                    return False
        except Exception as e:
            logging2.error(f'error:{e} {traceback.print_exc()}')
            return False


    def put(self, obj):
        while True:
            try:
                self.m_AQueue.put(obj, timeout=5)
                break
            except:
                time.sleep(1)
                logging2.error(f'm_AQueue size: {self.m_AQueue.qsize()} is full, waiting for empty')
                continue


    def stop(self):
        self.m_isExit = True


    def getExitFlag(self):
        return self.m_isExit


    def initOtherCfg(self):
        self.m_dicLatnKpiCode = {}
        if self.m_otherCfgObj.get('latn_kpicode_replace') is None:
            self.m_dicLatnKpiCode = {}
        else:
            self.m_latn_kpicode_replace = self.m_otherCfgObj['latn_kpicode_replace']
            self.m_dicLatnKpiCode = {}
            lstValue = self.m_latn_kpicode_replace.split(';')
            for value in lstValue:
                lstTmp = value.split('|')
                self.m_dicLatnKpiCode[lstTmp[0]] = lstTmp[1]
        self.m_dicCheckKpiCode = {}
        if self.m_otherCfgObj.get('check_kpicode_num') is None:
            self.m_dicCheckKpiCode = {}
        else:
            self.m_check_kpicode = self.m_otherCfgObj['check_kpicode_num']
            self.m_dicCheckKpiCode = {}
            lstValue = self.m_check_kpicode.split(';')
            for value in lstValue:
                lstTmp = value.split('|')
                self.m_dicCheckKpiCode[lstTmp[0]] = lstTmp[1]
        self.m_merge_max_secs = 60
        if self.m_otherCfgObj.get('merge_max_secs') is None:
            self.m_merge_max_secs = 60
        else:
            self.m_merge_max_secs = int(self.m_otherCfgObj['merge_max_secs'])


    def createProducer(self):
        producer = None
        try:
            if self.m_kafkaCfgObj.get('safe_cert') is None:
                logging2.error(f"safe_cert is None")
                return producer

            if self.m_kafkaCfgObj['safe_cert'] == '1':
                producer = KafkaProducer(
                    bootstrap_servers=self.m_kafkaCfgObj['bootstrap_servers'],
                    value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8'),
                    security_protocol=self.m_kafkaCfgObj['security_protocol'],  # 添加安全协议
                    sasl_mechanism=self.m_kafkaCfgObj['sasl_mechanism'],  # 添加SASL机制
                    sasl_plain_username=self.m_kafkaCfgObj['sasl_plain_username'],  # 添加用户名
                    sasl_plain_password=self.m_kafkaCfgObj['sasl_plain_password']  # 添加密码
                )
            else:
                producer = KafkaProducer(
                    bootstrap_servers=self.m_kafkaCfgObj['bootstrap_servers'],
                    value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8')
                )

            return producer
        except Exception as e:
            logging2.error(f"{str(e)}")
            return producer


    def createKafkaObj(self):
        self.m_producer = None
        try:
            self.m_producer = self.createProducer()
            try:
                metrics = self.m_producer.metrics()
                print("生产者连接成功，metrics信息:", metrics)
            except AttributeError:
                logging2.error(f"生产者连接可能失败[{self.m_kafkaCfgObj['bootstrap_servers']}]，无法获取metrics")
                logging2.stop()
                sys.exit()
            logging2.info(f"kafka [{self.m_kafkaCfgObj['bootstrap_servers']}] init success")
        except Exception as e:
            logging2.error(f"kafka [{self.m_kafkaCfgObj['bootstrap_servers']}] init failed: {e}")
            logging2.stop()
            sys.exit()


    def checkKafkaConn(self):
        try:
            producer = self.createProducer()
            try:
                metrics = producer.metrics()
            except AttributeError:
                logging2.error(f"生产者连接可能失败[{self.m_kafkaCfgObj['bootstrap_servers']}]，无法获取metrics")
                return False
            producer.close()
            return True
        except Exception as e:
            logging2.error(f"kafka [{self.m_kafkaCfgObj['bootstrap_servers']}] connect failed, {e}")
            return False      


    def releaseKafkaObj(self):
        try:
            self.m_producer.close()
            logging2.info(f'kafka release success')
        except Exception as e:
            logging2.error(f'kafka release failed: {e}')


    # 创建线程池
    def createThreadPool(self):
        self.m_threadPool = [] # 线程池列表，用于存储线程对象。
        self.m_markThread = [False] * self.m_maxThreadTaskNum


    # 生产消息
    def produceMsg(self, dicAll):
        # 发送消息
        strJson = json.dumps(dicAll, ensure_ascii=False)
        if self.checkKafkaConn() == False:
            logging2.error(f'produceMsg error, message[{strJson}]')
            return False
        try:
            logging2.info(f'produceMsg, message[{strJson}]')
            self.m_producer.send(self.m_topic, dicAll)
            self.m_producer.flush()
            logging2.info(f'produceMsg success, message[{strJson}]')
        except Exception as e:
            logging2.error(f'produceMsg error: {e}, message[{strJson}]')
            return False
        return True


    def taskQuery(self):
        try:
            sqlText = self.m_taskSqlCfgObj['sql_query']
            result = self.m_dbmanObj.execute_query(sqlText) # 获取任务结果。
            if len(result) == 0: # 如果任务结果为空，则返回False。
                logging2.warn('Task result is empty') 
                return [] # 返回False。
            else:
                return list(result)
        except Exception as e:
            logging2.error(f'Query task error: {e}')
            return []
        

    def taskInsert(self, lstTask):
        insertSqlText = self.m_taskSqlCfgObj['sql_insert']
        # 将[MM]替换为当前月份，例如1月份替换为01，2月份替换为02，以此类推。
        insertSqlText = insertSqlText.replace('[MM]', time.strftime('%m', time.localtime()))
        for result in lstTask:
            logging2.info(f'Insert task: {result}')
            logging2.info(f'Insert task: {insertSqlText}')
            sqlText = insertSqlText % (result)
            logging2.info(f'Insert task: {sqlText}')
            if self.m_dbmanObj.execute_sql(sqlText) == 0:
                logging2.error(f'Insert task error: {result}')
                return False
        return True


    def taskUpdate(self, lstTask):
        strMsgId = r''
        for task in lstTask:
            strMsgId += str(task[0]) + ','
        # 去掉最后一个逗号
        strMsgId = strMsgId[:-1]
        sqlText = self.m_taskSqlCfgObj['sql_update'] % (strMsgId)
        return self.m_dbmanObj.execute_sql(sqlText)


    def taskError(self, lstTask):
        strMsgId = r''
        for task in lstTask:
            strMsgId += str(task[0]) + ','
        # 去掉最后一个逗号
        strMsgId = strMsgId[:-1]
        sqlText = self.m_taskSqlCfgObj['sql_error'] % (strMsgId)
        return self.m_dbmanObj.execute_sql(sqlText)


    def taskTimeout(self, lstTask):
        strMsgId = r''
        for task in lstTask:
            strMsgId += str(task[0]) + ','
        # 去掉最后一个逗号
        strMsgId = strMsgId[:-1]
        sqlText = self.m_taskSqlCfgObj['sql_timeout'] % (strMsgId)
        return self.m_dbmanObj.execute_sql(sqlText)


    def taskDelete(self, lstTask):
        strMsgId = r''
        for task in lstTask:
            strMsgId += str(task[0]) + ','
        # 去掉最后一个逗号
        strMsgId = strMsgId[:-1]
        sqlText = self.m_taskSqlCfgObj['sql_delete'] % (strMsgId)
        return self.m_dbmanObj.execute_sql(sqlText)
