# 多线程执行任务，需要考虑线程安全，使用线程锁，或者使用线程池。
import threading
import time,sys
import logging2
import json
import traceback
from functools import partial
#from confluent_kafka import Producer
from kafka import KafkaProducer

from DCConfig import *
from DCDBMan import *
from DCTaskDeal import *

class DCTaskDist(threading.Thread):
    def __init__(self, configObj):
        threading.Thread.__init__(self) # 初始化线程，继承父类threading.Thread的__init__方法。
        self.m_configObj = configObj # 配置对象，用于获取配置信息。
        self.initParam()

    
    def initParam(self):
        self.m_dbCfgObj = self.m_configObj.get_group_value('common/dbconnect')[0] # 数据库配置对象，用于获取数据库配置信息。
        self.m_kafkaCfgObj = self.m_configObj.get_group_value('common/kafka')[0] # kafka配置对象，用于获取kafka配置信息。
        self.m_taskThreadCfgObj = self.m_configObj.get_group_value('common/taskThread')[0] # 任务线程配置对象，用于获取任务线程配置信息。
        self.m_taskSqlCfgObj = self.m_configObj.get_group_value('businessCfg/taskSQL')[0] # 任务SQL配置对象，用于获取任务SQL配置信息。
        self.m_otherCfgObj = self.m_configObj.get_group_value('businessCfg/otherCfg')[0] # 其他配置对象，用于获取其他配置信息。
        self.m_maxThreadTaskNum = int(self.m_taskThreadCfgObj['thread_num']) # 线程数量，用于创建线程池。
        self.m_isExit = False
        self.m_isRun = False
        self.m_threadPool = {}
        self.createDBObj()
        self.createThreadPool()

    
    def run(self) -> None:
        while True:
            try:
                if self.getExitFlag():
                    self.stop()
                    break
                else:
                    if self.m_isRun == False:
                        time.sleep(3)
                        continue

                    lstTask = self.taskQuery()
                    if lstTask != None and len(lstTask) == 0:
                        time.sleep(3)
                        continue
                    else:
                        if self.taskUpdate(lstTask) == 0:
                            logging2.error('update task status error.')
                            continue
                    for task in lstTask:
                        keyStr = str(task[9])+'|'+str(task[5])+'|'+str(task[7])
                        hash_value = hash(keyStr)
                        thread_index = abs(hash_value) % self.m_maxThreadTaskNum
                        if self.m_threadPool.get(thread_index) is not None:
                            self.m_threadPool[thread_index].put(task)
                        else:
                            logging2.error(f'thread pool error, thread_index:{thread_index}')
                            continue
            except Exception as e:
                logging2.error(f'run error:{e}')
                continue

    # 0. msg_id
    # 1. system
    # 2. subsys
    # 3. Module
    # 4. Module_name
    # 5. Latn_id
    # 6. Latn_name
    # 7. id_code
    # 8. id_name
    # 9. batch_id
    # 10. startTime
    # 11. endTime
    # 12. sendTime
    # 13. areaCode
    # 14. areaName
    # 15. Kpi_Code
    # 16. Kpi_name
    # 17. Kpi_value
    # 18. Create_date
    # 19. Deal_flag
    # 20. deal_date


    def stop(self):
        for i in range(0, self.m_maxThreadTaskNum):
            if self.m_threadPool.get(i) is not None:
                self.m_threadPool[i].stop()
                self.m_threadPool[i].join()
        self.m_dbmanObj.close()


    def resetIsExit(self, flag):
        self.m_isExit = flag


    def resetIsRun(self, flag):
        self.m_isRun = flag


    def getExitFlag(self):
        return self.m_isExit


    def createDBObj(self):
        self.m_dbmanObj = None
        dbtype = self.m_dbCfgObj['dbtype']
        if dbtype == 'mysql':
            self.m_dbmanObj = DCDBMan('mysql', self.m_dbCfgObj)
        elif dbtype == 'pg':
            self.m_dbmanObj = DCDBMan('pg', self.m_dbCfgObj)
        if self.m_dbmanObj == None:
            logging2.error(f'db init failed[{dbtype}]')
            logging2.stop()
            sys.exit()
        else:
            if self.m_dbmanObj.connect() != None:
                logging2.info('db init success')
            else:
                logging2.error('db init failed')
                logging2.stop()
                sys.exit()


    # 创建线程池
    def createThreadPool(self):
        self.m_threadPool = {} # 线程池列表，用于存储线程对象。
        for i in range(0, self.m_maxThreadTaskNum):
            dcTaskDealObj = DCTaskDeal(self.m_configObj, self.m_dbmanObj)
            dcTaskDealObj.start()
            self.m_threadPool[i] = dcTaskDealObj


    def taskQuery(self):
        try:
            sqlText = self.m_taskSqlCfgObj['sql_query']
            result = self.m_dbmanObj.execute_query(sqlText) # 获取任务结果。
            if len(result) == 0: # 如果任务结果为空，则返回False。
                logging2.warn('Task result is empty') 
                return [] # 返回False。
            else:
                return list(result)
        except Exception as e:
            logging2.error(f'Query task error: {e}')
            return []


    def taskUpdate(self, lstTask):
        strMsgId = r''
        for task in lstTask:
            strMsgId += str(task[0]) + ','
        # 去掉最后一个逗号
        strMsgId = strMsgId[:-1]
        sqlText = self.m_taskSqlCfgObj['sql_update'] % (strMsgId)
        return self.m_dbmanObj.execute_sql(sqlText)