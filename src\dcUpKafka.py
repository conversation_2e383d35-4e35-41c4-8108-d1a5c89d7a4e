import sys
import logging2
import signal
from DCConfig import *
from DCDBMan import *
from DCTaskDist import *
import zkNodeMoni
from zkNodeMoni import *
from zkCheckThread import *

global g_isExit
g_isExit = False
global g_running
g_running = False

# 设置信号量退出
def handle_sigterm(signum, frame):
    logging2.warn("Received SIGTERM, shutting down gracefully.")
    global g_isExit
    g_isExit = True



def init_zk(configObj):
    try:
        zk_cfg = configObj.get_group_value('common/zk')[0]
        print(zk_cfg)
        if zk_cfg['zkSwitch'] == '1':
            zkNodeMoniObj = zkNodeMoni(zk_cfg['zkAddr'], zk_cfg['zkNode'])
            if zkNodeMoniObj.zkStart() == False:
                logging2.error("start zk err, exit, [%s][%s]" % (zk_cfg["zkAddr"], zk_cfg["zkNode"]))
                logging2.stop()
                sys.exit()
            return zkNodeMoniObj
        return None
    except Exception as err:
        # 将输出重定向到字符串缓冲区
        buf = io.StringIO()
        traceback.print_exc(file=buf)
        # 获取缓冲区的内容
        error_msg = buf.getvalue()
        logging2.error("err[%s][%s]" % (str(err), str(error_msg)))
        return None


if __name__ == '__main__':
    if len(sys.argv) != 2:
        print(" e.g.: %s xmlfile" % sys.argv[0])
        sys.exit()

    xmlfile = sys.argv[1]
    config_node = ['common/log', 'common/mysql', 'common/zk', 'common/dbconnect', 'common/kafka', 'common/taskThread', 'businessCfg/taskSQL', 'businessCfg/otherCfg']
    configObj = DCConfig(xmlfile, config_node)
    configObj.load_config_new()

    # 初始化日志
    log_config = list()
    log_config = configObj.get_group_value('common/log')
    if log_config == None:
        print("common/log is not set")
        sys.exit()
    else:
        logging2.start(log_config[0]['logpath'], 'DCUpKafka', log_config[0]['loglevel'])
        logging2.info("log init success logpath=%s loglevel=%s" % (log_config[0]['logpath'], log_config[0]['loglevel']))

    signal.signal(signal.SIGTERM, handle_sigterm)
    zkNodeMoniObj = init_zk(configObj)
    zkCheckThreadObj = None
    if zkNodeMoniObj:
        zkCheckThreadObj = zkCheckThread(zkNodeMoniObj)
        zkCheckThreadObj.start()
    # 初始化任务
    dctask = DCTaskDist(configObj)
    dctask.start()
    while True:
        if zkCheckThreadObj:
            dctask.resetIsRun(zkCheckThreadObj.getIsRunning())
        else:
            dctask.resetIsRun(True)
        # 设置退出状态，优雅退出
        dctask.resetIsExit(g_isExit)
        # 获取任务退出状态
        if dctask.getExitFlag():
            logging2.warn("All tasks have exited, main thread ready to exit, Wait a few seconds...")
            break

        # 参数动态生效
        if configObj.update_config():
            cfg_value = configObj.get_single_value('log', 'logLevel')
        time.sleep(10)
    # 设置SIGTERM信号的处理器
    dctask.join()
    logging2.stop()

    if zkCheckThreadObj:
        zkCheckThreadObj.stop()
        zkCheckThreadObj.join()