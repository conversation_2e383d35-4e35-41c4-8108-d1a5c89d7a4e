#!/usr/bin/python
# -*- coding: UTF-8 -*-

import os
import threading
import queue
import time
import datetime
import logging
#import logging.handlers
#from logging.handlers import TimedRotatingFileHandler
from logging.handlers import RotatingFileHandler
import traceback


def MkDirMul(path):
    try:
        if len(path) <= 0 or path[0] != '/':
            return False
        path = path + '/'
        if os.path.isdir(path):
            return True
        else:
            dirTmp = r''
            for x in path:
                dirTmp = dirTmp + x
                if x == '/':
                    if os.path.isdir(dirTmp) == False:
                        os.mkdir(dirTmp)
    except Exception as err:
        print(str(err))
        return False


class logging2(threading.Thread):
    AQueue = queue.Queue(100000)
    nPID = os.getpid()
    Adt = datetime.datetime.now().strftime('%Y%m%d')
    nCount = 1
    nThreadNo = 0
    nState = 1
    mylogLevel = "DEBUG"

    def __init__(self, threadID, logpath, name, module, logLevel):
        threading.Thread.__init__(self)
        self.threadID = threadID
        self.logpath = logpath
        self.name = name
        self.m_module = module
        logging2.mylogLevel = logLevel
        self.m_logger = None
        self.m_handle = None
        self.m_console = None
        self.initLogging()


    def initLogging(self):
        try:
            if self.m_logger == None:
                print("set module[%s] loglevel: [%s]" % (self.m_module, logging2.mylogLevel))
                formatter = logging.Formatter('%(asctime)s|%(name)s|%(process)d|%(levelname)s|%(message)s')
                self.m_logfile = self.logpath + "/log_" + self.m_module + "_" + str(logging2.nPID) + ".log"
                self.m_logger = logging.getLogger(self.m_module)
        
                # self.m_handle = TimedRotatingFileHandler(self.m_logfile, 'midnight' , 1 , 0)
                # self.m_handle.suffix = "%Y%m%d-%H%M.log"
                self.m_handle = RotatingFileHandler(self.m_logfile, maxBytes=50*1024*1024, backupCount=150)
                self.m_handle.setFormatter(formatter)

                self.m_console = logging.StreamHandler()
                self.m_console.setFormatter(formatter)

                self.m_logger.setLevel(level=logging.DEBUG)
                self.m_handle.setLevel(logging.DEBUG)
                self.m_console.setLevel(logging.DEBUG)

                self.m_logger.addHandler(self.m_handle)
                self.m_logger.addHandler(self.m_console)
            else:
                try:
                    self.m_logger = logging.shutdown(self.m_handle)
                except:
                    pass
                del self.m_logger
                del self.m_handle
                del self.m_console
                self.m_logger = None
                self.m_handle = None
                self.m_console = None
                self.initLogging()
        except Exception as err:
            traceback.print_exc()
            print(str(err))


    def reSetLogLevel(self, logLevel):
        logging2.mylogLevel = logLevel


    def run(self):
        print("开启日志线程：" + self.name)
        times = 0
        while True:
            try:
                if os.path.exists(self.m_logfile) == False:
                    self.initLogging()
                    time.sleep(1)
                if logging2.AQueue.empty() and logging2.nState == 0:
                    times = times + 1
                    time.sleep(1)
                else:
                    times = 0
                if times >= 3:
                    break
            
                if logging2.AQueue.empty() == False:
                    # 从队列获取日志消息
                    data = logging2.AQueue.get()
                    # 解析日志消息，格式：日志级别，内容
                    level = list(data.keys())[0]
                    content = data.get(level)
                    # 把内容按分隔符^解析成list传入参数
                    lstContent = list(content.split('^'))
                    if self.m_logger:
                        if level == 'DEBUG':
                            self.m_logger.debug(*lstContent)
                        elif level == 'INFO':
                            self.m_logger.info(*lstContent)
                        elif level == 'WARNING':
                            self.m_logger.warning(*lstContent)
                        elif level == 'ERROR':
                            self.m_logger.error(*lstContent)
                else:
                    time.sleep(0.2)
            except Exception as err:
                traceback.print_exc()
                print("logging2", err)


def trackFileInfo(logMsg):
    try:
        s =  traceback.extract_stack()
        depC = 3
        if len(s) >= depC:
            filename = s[len(s)-depC].filename
            lstV = filename.split("/")
            return str(threading.current_thread().ident) + "|" + lstV[len(lstV)-1] + ":" + str(s[len(s)-depC].lineno) + "|" + s[len(s)-depC].name + "|" + logMsg
        return logMsg
    except Exception as err:
        traceback.print_exc()
        print(err)
        return ''


def debug(*content):
    try:
        if logging2.mylogLevel == "DEBUG":
            logMsg = ""
            # 传入多个参数用竖线分隔符分开
            for i in range(len(content)):
                if i == len(content) - 1:
                    logMsg += content[i]
                else:
                    logMsg += content[i] + "^"
            logging2.AQueue.put({'DEBUG': trackFileInfo(logMsg)})
    except Exception as err:
        traceback.print_exc()
        print(err)


def info(*content):
    try:
        if logging2.mylogLevel == "INFO" or logging2.mylogLevel == "DEBUG":
            logMsg = ""
            for i in range(len(content)):
                if i == len(content) - 1:
                    logMsg += content[i]
                else:
                    logMsg += content[i] + "^"
            logging2.AQueue.put({'INFO': trackFileInfo(logMsg)})
    except Exception as err:
        traceback.print_exc()
        print(err)


def warn(*content):
    try:
        if logging2.mylogLevel == "WARNING" or logging2.mylogLevel == "INFO" or logging2.mylogLevel == "DEBUG":
            logMsg = ""
            for i in range(len(content)):
                if i == len(content) - 1:
                    logMsg += content[i]
                else:
                    logMsg += content[i] + "^"
            logging2.AQueue.put({'WARNING': trackFileInfo(logMsg)})
    except Exception as err:
        traceback.print_exc()
        print(err)


def error(*content):
    try:
        if logging2.mylogLevel == "ERROR" or logging2.mylogLevel == "WARNING" or logging2.mylogLevel == "INFO" or logging2.mylogLevel == "DEBUG":
            logMsg = ""
            for i in range(len(content)):
                if i == len(content) - 1:
                    logMsg += content[i]
                else:
                    logMsg += content[i] + "^"
            logging2.AQueue.put({'ERROR': trackFileInfo(logMsg)})
    except Exception as err:
        traceback.print_exc()
        print(err)


def reSetLogLevel(logLevel):
    if logLevel == "DEBUG" or logLevel == "INFO" or logLevel == "WARNING" or logLevel == "ERROR":
        logging2.mylogLevel = logLevel
        print("reSetLogLevel[%s] success.. " % logLevel)
    else:
        print("invalid logLevel[%s], please select[DEBUG, INFO, WARNING, ERROR], reSetLogLevel failed" % logLevel)
        return


def start(logpath, module, level):
    try:
        MkDirMul(logpath)
        logging2.nThreadNo = logging2.nThreadNo +1
        # 创建新线程
        thread1 = logging2(logging2.nThreadNo, logpath, "Thread-log", module, level)
        # 开启新线程
        thread1.start()
        #thread1.join()
    except Exception as err:
        traceback.print_exc()
        print(err)


def stop():
    try:
        logging2.nState = 0
    except Exception as err:
        traceback.print_exc()
        print(err)