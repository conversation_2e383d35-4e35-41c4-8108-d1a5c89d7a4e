# centos+x86，26编译不了，到27编译/codebill/AH_MYSQL/src_5g/CTPC/DCUpKafka/src
# ctyunos+arm，在151编译，先执行环境变量env.sh
# 27-codebill
cd /codebill/AH_MYSQL/src_5g/CTPC/DCUpKafka/src
pyinstaller --clean --onefile -F --runtime-tmpdir ./.mytmp --name dcUpKafka dcUpKafka.py
pyinstaller --clean --onefile -F --runtime-tmpdir ./.mytmp --name desTool desTool.py

# 151-codebill
cd /public/codebill/AH_MYSQL/src_5g/CTPC/DCUpKafka/src
pyinstaller --clean --onefile -F --runtime-tmpdir ./.mytmp --name dcUpKafka_arm dcUpKafka.py
pyinstaller --clean --onefile -F --runtime-tmpdir ./.mytmp --name desTool_arm desTool.py

# 166-codebill
cd /public/codebill/AH_MYSQL/src_5g/CTPC/DCUpKafka/src
pyinstaller --clean --onefile -F --runtime-tmpdir ./.mytmp --name dcUpKafka_x86 dcUpKafka.py
pyinstaller --clean --onefile -F --runtime-tmpdir ./.mytmp --name desTool_x86 desTool.py