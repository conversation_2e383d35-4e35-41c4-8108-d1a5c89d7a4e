# from confluent_kafka import Producer

# # Kafka配置
# conf = {
#     'bootstrap.servers': '192.168.161.27:9092',  # Kafka服务器地址
#     'client.id': 'python-producer'         # 生产者ID
# }

# # 创建生产者实例
# producer = Producer(**conf)

# # 发送消息的回调函数
# def delivery_report(err, msg):
#     if err is not None:
#         print(f'Message delivery failed: {err}')
#     else:
#         print(f'Message delivered to {msg.topic()} [{msg.partition()}]')

# # 要发送的消息
# topic = 'test'
# messages = [
#     'Hello, Kafka!',
#     'This is a test message.',
#     'Kafka is awesome!'
# ]

# # 发送消息
# for message in messages:
#     producer.produce(topic, message.encode('utf-8'), callback=delivery_report)

# # 等待所有消息发送完毕
# producer.flush()


# str = '2024-12-04 16:00:00+8:00'
# pos = str.find('+')
# print(str[:pos])


test_dict = {'DCM20250205131801770|999|CY1001': {'time': 23}, 'DCM20250205131801770|999|CY1002': {'time': 24}, 'DCM20250205131801770|999|CY1003': {'time': 25}}
print(test_dict)

for key in test_dict.keys():
    if key == 'DCM20250205131801770|999|CY1001':
        del test_dict[key]
    print(test_dict)