
import threading
import time
import logging2


class zkCheckThread(threading.Thread):
    def __init__(self, zkNodeMoniObj):
        threading.Thread.__init__(self) # 初始化线程，继承父类threading.Thread的__init__方法。
        self.m_zkNodeMoniObj = zkNodeMoniObj
        self.m_isRunning = False
        self.m_isExit = False

    def run(self):
        while True:
            if self.m_isExit:
                break

            flag = self.m_zkNodeMoniObj.checkZkNode()
            if flag:
                self.m_isRunning = True
                logging2.info("I'm master, keep running...")
            else:
                self.m_isRunning = False
                logging2.info("I'm slaver, Waiting to take over...")
            time.sleep(5)
    
    def stop(self):
        self.m_isExit = True

    def getIsRunning(self):
        return self.m_isRunning