#!/usr/bin/python
# -*- coding: utf-8 -*-

import socket
import traceback
from kazoo.client import KazooClient
import logging2
import os


class zkNodeMoni:
    def __init__(self, zkAddr, zkNode):
        self.m_zkAddr = zkAddr
        self.m_zkNode = zkNode
        self.batchID = ""
        self.oldBatchID = ""
        self.m_zk = None
        self.m_ip = r''

    def zkStart(self):
        try:
            self.m_zk = KazooClient(hosts=self.m_zkAddr) 
            self.m_zk.start()
            logging2.info("start zk success[%s][%s]" % (self.m_zkAddr, self.m_zkNode))
            return True
        except Exception as err:
            logging2.error(str(err))
            logging2.error("start zk failed[%s][%s]" % (self.m_zkAddr, self.m_zkNode))
            return False

    def get_ip_address(self):
        if self.m_ip != r'':
            return self.m_ip
        self.m_ip = socket.gethostbyname(socket.getfqdn(socket.gethostname(  )))
        # 获取本机IP地址
        return self.m_ip

    def checkZkNode(self):
        try:
            logging2.info("start checking zkNode[%s][%s]" % (self.m_zkAddr, self.m_zkNode))
            if self.m_zk == None:
                self.zkStart()
            # zk = KazooClient(hosts=self.m_zkAddr)    #如果是本地那就写127.0.0.1
            # zk.start()    #与zookeeper连接
            self.m_zk.ensure_path(os.path.dirname(self.m_zkNode))
            if self.m_zk.exists(self.m_zkNode):
                nodeinfo = self.m_zk.get(self.m_zkNode)
                if len(nodeinfo) > 0:
                    lstValue = nodeinfo[0].decode("utf-8").split('|')
                    ipStr = str(self.get_ip_address())
                    pidStr = str(os.getpid())
                    if str(lstValue[0]) == ipStr and str(lstValue[1]) == pidStr:
                        logging2.info("checkZkNode[%s][%s] exists, info[%s]" % (self.m_zkAddr, self.m_zkNode, nodeinfo[0]))
                        return True
                    else:
                        logging2.warn("checkZkNode[%s][%s] exists, info[%s], not [%s]" % (self.m_zkAddr, self.m_zkNode, nodeinfo[0], ipStr+"|"+pidStr))
                        return False
            else:
                zkStr = str(self.get_ip_address()) + "|" + str(os.getpid())
                self.m_zk.create(path=self.m_zkNode, value=zkStr.encode('utf-8'), ephemeral=True)
                logging2.info("checkZkNode[%s][%s] not exists, set it[%s]" % (self.m_zkAddr, self.m_zkNode, zkStr))
                return True         
        except Exception as err:
            traceback.print_exc()
            logging2.error("zk error[%s][%s][%s]" % (self.m_zkAddr, self.m_zkNode,str(err)))
            self.zkClose()
            return False

    def zkClose(self):
        try:
            self.m_zk.stop()
            self.m_zk = None
        except Exception as err:
            self.m_zk = None
            logging2.error(str(err))