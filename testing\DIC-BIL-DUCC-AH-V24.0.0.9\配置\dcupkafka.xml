<DCUpKafka>
    <common>
        <log>
            <param name="logpath">/codebill/AH_MYSQL/src_5g/CTPC/DCUpKafka/log</param>
            <param name="loglevel">DEBUG</param><!--DEBUG, INFO, WARNING, ERROR-->
        </log>    

        <dbconnect>
            <param name="dbtype">mysql</param><!--mysql, pg，二选一-->
            <param name="host">**************</param>
            <param name="port">3308</param>
            <param name="user">root</param>
            <param name="pass">mysql</param><!--明文密文都支持，加解密用desTool工具-->
            <param name="db">bp-ticket</param>
        </dbconnect>

        <kafka>
            <param name="bootstrap_servers">**************:9092</param>
            <param name="client_id">DCUpKafka</param><!--自定义-->
            <param name="topic">test</param>
        </kafka>

        <taskThread>
            <param name="thread_num">3</param>
        </taskThread>
    </common>

    <businessCfg>
        <taskSQL>
            <param name="sql_query">SELECT msg_id,system,subsys,Module,Module_name,Latn_id,Latn_name,id_code,id_name,batch_id,startTime,endTime,sendTime,areaCode,areaName,Kpi_Code,Kpi_name,Kpi_value,Create_date,Deal_flag,deal_date FROM kafka_upload_messages where Deal_flag=0 LIMIT 2</param>
            <param name="sql_update">update kafka_upload_messages set Deal_flag=1,deal_date=now() where msg_id in (%s)</param>
            <param name="sql_error">update kafka_upload_messages set Deal_flag=4,deal_date=now() where msg_id in (%s)</param>
            <param name="sql_delete">delete from kafka_upload_messages where msg_id in (%s)</param>
            <param name="sql_insert">insert into kafka_upload_messages_[MM](msg_id,system,subsys,Module,Module_name,Latn_id,Latn_name,id_code,id_name,batch_id,startTime,endTime,sendTime,areaCode,areaName,Kpi_Code,Kpi_name,Kpi_value,Create_date,Deal_flag,deal_date) values('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')</param>
        </taskSQL>
        
        <otherCfg>
            <param name="latn_kpicode_replace">CY1002|CZ1001L04;CY1003|CZ1003L06;CL1001|CL1001L09</param><!--格式：latn_id|kpi_code, idcode1|kpicode1;idcode2|kpicode2;idcode3|kpicode3-->
        </otherCfg>

    </businessCfg>

</DCUpKafka>
